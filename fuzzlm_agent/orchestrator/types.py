"""Consolidated type definitions for the orchestrator module.

This module contains all TypedDict definitions used across the orchestrator components,
organized into logical categories for better maintainability and reusability.
"""

from datetime import datetime
from typing import Any, Optional, TypedDict


# ============================================================================
# Process Types
# ============================================================================


class TaskStatus(TypedDict):
    """Status of a monitoring task."""

    name: str
    state: str  # running, failed, restarting
    last_heartbeat: datetime
    restart_count: int
    error: str | None


class ShadowProcessInfo(TypedDict):
    """Shadow process configuration information."""

    id: str
    state: str
    created_at: str
    promotion_score: float
    performance_history_length: int
    resource_usage: Optional[dict[str, float]]
    last_health_check: Optional[str]


class FuzzerProcessInfo(TypedDict):
    """Generic fuzzer process information."""

    id: str
    type: str  # champion, shadow
    state: str
    created_at: str
    target_path: str
    strategy_name: str


# ============================================================================
# Metrics Types
# ============================================================================


class PerformanceMetrics(TypedDict):
    """Performance metrics dictionary structure."""

    coverage: float
    crashes_found: int
    exec_per_sec: float
    corpus_size: int
    timestamp: float
    memory_usage_mb: Optional[float]
    cpu_percent: Optional[float]


class RuntimeMetrics(TypedDict):
    """Runtime performance metrics."""

    total_executions: int
    execution_rate: float
    coverage_hits: int
    unique_edges: int
    path_discovery_rate: float
    crashes_found: int
    crash_rate: float
    corpus_size: int
    corpus_growth_rate: float
    efficiency_score: float
    instance_id: str


class ResourceUsage(TypedDict):
    """Resource usage tracking."""

    memory_mb: float
    cpu_percent: float
    peak_memory_mb: Optional[float]
    peak_cpu_percent: Optional[float]


# ============================================================================
# Strategy Types
# ============================================================================


class MutatorConfig(TypedDict):
    """Mutator configuration."""

    type: str
    probability: float
    parameters: dict[str, Any]


class SchedulerConfig(TypedDict):
    """Scheduler configuration."""

    type: str
    parameters: dict[str, Any]


class FeedbackConfig(TypedDict):
    """Feedback configuration."""

    type: str
    parameters: dict[str, Any]


class ShadowStrategy(TypedDict):
    """Shadow strategy configuration dictionary."""

    name: str
    custom_code: str
    resource_limit: float
    mutators: list[dict[str, Any]]
    scheduler: dict[str, Any]
    feedback: dict[str, Any]


class StrategyUpdate(TypedDict):
    """Strategy update request."""

    mutators: list[MutatorConfig]
    scheduler: SchedulerConfig
    feedback: FeedbackConfig
    custom_code: Optional[str]
    metadata: dict[str, Any]


# ============================================================================
# Event Types
# ============================================================================


class TelemetryEntry(TypedDict):
    """Telemetry data entry."""

    timestamp: float
    type: str
    fuzzer_id: Optional[str]
    instance_id: Optional[str]
    data: dict[str, Any]


class ShadowEvent(TypedDict):
    """Base shadow process event."""

    timestamp: float
    event_type: str
    shadow_id: Optional[str]


class CreateShadowEventData(ShadowEvent):
    """Shadow creation event data."""

    reason: str
    new_strategy_code: str
    confidence_score: float


class PromoteShadowEventData(ShadowEvent):
    """Shadow promotion event data."""

    performance_gain: float


class TerminateShadowEventData(ShadowEvent):
    """Shadow termination event data."""

    reason: str


# ============================================================================
# Status Types
# ============================================================================


class ShadowStatus(TypedDict):
    """Shadow process status dictionary."""

    id: str
    state: str
    created_at: str
    promotion_score: float
    performance_history_length: int
    resource_usage: Optional[dict[str, float]]
    last_health_check: Optional[str]


class ManagerStatus(TypedDict):
    """Shadow process manager status."""

    active_shadows: int
    total_shadows: int
    resource_usage: dict[str, float]
    config: dict[str, Any]
    shadows: dict[str, ShadowStatus]


class CampaignStatus(TypedDict):
    """Campaign execution status."""

    phase: str
    state: str
    start_time: str
    elapsed_time: float
    champion_fuzzer_id: Optional[str]
    shadow_processes: list[str]
    metrics_summary: dict[str, float]


# ============================================================================
# Analysis Types
# ============================================================================


class StateAnalysisData(TypedDict):
    """LLM state analysis response data."""

    current_stage: str
    confidence: float
    bottlenecks: list[str]
    needs_adjustment: bool
    suggested_actions: list[str]
    recommendations: Optional[list[str]]


class PerformanceReport(TypedDict):
    """Performance analysis report."""

    summary: dict[str, float]
    trends: dict[str, str]
    anomalies: list[str]
    recommendations: list[str]
    timestamp: str


# ============================================================================
# Configuration Types
# ============================================================================


class ShadowConfig(TypedDict):
    """Shadow process configuration."""

    max_concurrent: int
    performance_window: int
    promotion_threshold: float
    timeout_hours: float
    resource_limit: float


class SystemConfig(TypedDict):
    """System configuration."""

    grpc_address: str
    telemetry_buffer_size: int
    max_memory_usage_mb: int
    cpu_limit_percent: float
    shadow: ShadowConfig
